{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "sourceMap": true, "composite": true, "incremental": true, "jsx": "react-jsx", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"], "references": [{"path": "../firebase-config"}, {"path": "../database"}, {"path": "../shared-types"}]}