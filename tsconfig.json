{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES6"], "types": ["node"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve"}, "references": [{"path": "./packages/shared-types"}, {"path": "./packages/shared-ui"}, {"path": "./packages/database"}, {"path": "./packages/api-client"}, {"path": "./packages/business-logic"}, {"path": "./packages/config"}, {"path": "./packages/firebase-config"}, {"path": "./packages/shared-utils"}, {"path": "./packages/shared-auth"}], "files": [], "include": []}