{"name": "shared-auth", "version": "0.1.0", "description": "Shared Firebase authentication package for Tap2Go monorepo", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"firebase": "^11.9.1", "firebase-config": "workspace:*", "database": "workspace:*", "shared-types": "workspace:*", "react": "^19.0.0"}, "devDependencies": {"@types/react": "^19.0.14", "typescript": "^5.8.3"}, "peerDependencies": {"react": "^19.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./contexts": {"types": "./dist/contexts/index.d.ts", "default": "./dist/contexts/index.js"}, "./hooks": {"types": "./dist/hooks/index.d.ts", "default": "./dist/hooks/index.js"}, "./services": {"types": "./dist/services/index.d.ts", "default": "./dist/services/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "default": "./dist/types/index.js"}}}